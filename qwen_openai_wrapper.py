#!/usr/bin/env python3
"""
OpenAI-compatible wrapper for UIUC hosted Qwen2.5-VL-72B-Instruct model
This allows using the Qwen API with OpenAI-style interfaces and tools
"""

import requests
import json
import time
from typing import List, Dict, Optional, Iterator, Union
import uuid

class QwenOpenAIWrapper:
    """OpenAI-compatible wrapper for Qwen API"""
    
    def __init__(self, api_key: str = "uc_0c8d4f0264ec48d0b6a7051b84c496b3"):
        self.base_url = "https://uiuc.chat/api/chat-api/chat"
        self.api_key = api_key
        self.headers = {'Content-Type': 'application/json'}
        self.model_name = "Qwen/Qwen2.5-VL-72B-Instruct"
    
    def _prepare_messages(self, messages: List[Dict]) -> List[Dict]:
        """Ensure messages have non-empty content"""
        prepared = []
        for msg in messages:
            content = msg.get("content", "").strip()
            if not content:
                # Add default content for empty messages
                if msg.get("role") == "system":
                    content = "You are a helpful assistant."
                elif msg.get("role") == "user":
                    content = "Hello"
                else:
                    content = "I understand."
            
            prepared.append({
                "role": msg.get("role", "user"),
                "content": content
            })
        return prepared
    
    def chat_completions_create(
        self,
        messages: List[Dict],
        model: Optional[str] = None,
        temperature: float = 0.1,
        stream: bool = False,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Union[Dict, Iterator[Dict]]:
        """
        OpenAI-compatible chat completions endpoint
        """
        # Prepare request data
        data = {
            "model": model or self.model_name,
            "messages": self._prepare_messages(messages),
            "api_key": self.api_key,
            "course_name": "modeluse",
            "stream": stream,
            "temperature": temperature,
            "retrieval_only": False
        }
        
        try:
            response = requests.post(self.base_url, headers=self.headers, json=data)
            
            if response.status_code != 200:
                raise Exception(f"API Error {response.status_code}: {response.text}")
            
            if stream:
                return self._stream_response(response)
            else:
                return self._non_stream_response(response)
                
        except Exception as e:
            raise Exception(f"Request failed: {e}")
    
    def _stream_response(self, response) -> Iterator[Dict]:
        """Handle streaming response in OpenAI format"""
        chunk_id = str(uuid.uuid4())
        
        for i, chunk in enumerate(response.iter_lines()):
            if chunk:
                content = chunk.decode()
                
                # OpenAI-style streaming response
                yield {
                    "id": f"chatcmpl-{chunk_id}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": self.model_name,
                    "choices": [{
                        "index": 0,
                        "delta": {
                            "content": content
                        },
                        "finish_reason": None
                    }]
                }
        
        # Final chunk
        yield {
            "id": f"chatcmpl-{chunk_id}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": self.model_name,
            "choices": [{
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }]
        }
    
    def _non_stream_response(self, response) -> Dict:
        """Handle non-streaming response in OpenAI format"""
        full_content = ""
        for chunk in response.iter_lines():
            if chunk:
                full_content += chunk.decode()
        
        return {
            "id": f"chatcmpl-{str(uuid.uuid4())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": self.model_name,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": full_content
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": -1,  # Not available from Qwen API
                "completion_tokens": -1,  # Not available from Qwen API
                "total_tokens": -1  # Not available from Qwen API
            }
        }

# OpenAI-style client interface
class QwenClient:
    """OpenAI-style client for Qwen API"""
    
    def __init__(self, api_key: str = "uc_0c8d4f0264ec48d0b6a7051b84c496b3"):
        self.chat = ChatCompletions(api_key)

class ChatCompletions:
    """OpenAI-style chat completions interface"""
    
    def __init__(self, api_key: str):
        self.wrapper = QwenOpenAIWrapper(api_key)
    
    def create(self, **kwargs):
        """Create chat completion (OpenAI-compatible)"""
        return self.wrapper.chat_completions_create(**kwargs)

# Example usage functions
def test_openai_style():
    """Test the OpenAI-style interface"""
    client = QwenClient()
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of France?"}
    ]
    
    # Non-streaming
    print("Non-streaming response:")
    response = client.chat.create(messages=messages, stream=False)
    print(json.dumps(response, indent=2))
    
    print("\n" + "="*50 + "\n")
    
    # Streaming
    print("Streaming response:")
    for chunk in client.chat.create(messages=messages, stream=True):
        if chunk["choices"][0]["delta"].get("content"):
            print(chunk["choices"][0]["delta"]["content"], end="", flush=True)
    print()

if __name__ == "__main__":
    test_openai_style()
