#!/usr/bin/env python3
"""
Final Working Qwen-Agent Implementation
Uses the official Qwen-Agent framework with proper UIUC API integration
"""

import os
import json
import requests
from qwen_agent.llm import get_chat_model
from qwen_agent.agents import Assistant

def test_simple_qwen_agent():
    """Test with a simple working configuration"""
    print("🚀 Testing Final Qwen-Agent Implementation...")
    print("=" * 80)
    
    # Configure LLM to use our OpenAI-compatible server
    llm_cfg = {
        'model': 'Qwen/Qwen2.5-VL-72B-Instruct',
        'model_server': 'http://localhost:8000/v1',
        'api_key': 'EMPTY',
        'generate_cfg': {
            'temperature': 0.1,
            'max_tokens': 2000,
        }
    }
    
    # Use built-in tools only
    tools = [
        'code_interpreter',  # Built-in code execution tool
    ]
    
    # Create the assistant
    bot = Assistant(
        llm=llm_cfg,
        function_list=tools,
        name='Final UIUC Qwen Agent',
        description="""I'm an AI assistant powered by Qwen2.5-VL-72B-Instruct running on UIUC infrastructure.

🔧 **My Capabilities:**
• **Code Interpreter** - Execute Python code with pandas, matplotlib, numpy support
• **Data Analysis** - Analyze data and create visualizations
• **Mathematical Computing** - Solve equations and calculations
• **File Operations** - Create, read, and manipulate files

I can actually execute code and create files, not just provide examples!"""
    )
    
    # Test queries
    test_queries = [
        "What is 15 factorial? Calculate it using Python.",
        "Create a CSV file with sample sales data (5 rows) and then create a bar chart from it."
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test {i}: {query}")
        print("-" * 60)
        
        # Run the query
        messages = [{'role': 'user', 'content': query}]
        
        try:
            response_text = ''
            for response in bot.run(messages=messages):
                for msg in response:
                    if hasattr(msg, 'content') and msg.content:
                        print(f"🤖 Assistant: {msg.content}")
                        response_text += msg.content
                    elif isinstance(msg, dict) and msg.get('content'):
                        print(f"🤖 Assistant: {msg['content']}")
                        response_text += msg['content']
            
            # Check if tools were actually used
            if 'import' in response_text or 'def ' in response_text or 'print(' in response_text:
                print("✅ Code execution detected!")
            else:
                print("⚠️  No code execution detected - tools may not be working")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 60)
    
    print("\n✅ All tests completed!")

def chat_interface():
    """Interactive chat interface"""
    print("🤖 Final Qwen-Agent Chat Interface")
    print("=" * 60)
    print("🔧 **Capabilities:**")
    print("• Code Interpreter - Execute Python code with full library support")
    print("• Data Analysis - Analyze and visualize data")
    print("• Mathematical Computing - Solve complex calculations")
    print("• File Operations - Create, read, and manipulate files")
    print("=" * 60)
    print("Commands: /quit (exit), /help (show help), /clear (clear history)")
    print("=" * 60)
    
    # Configure LLM
    llm_cfg = {
        'model': 'Qwen/Qwen2.5-VL-72B-Instruct',
        'model_server': 'http://localhost:8000/v1',
        'api_key': 'EMPTY',
        'generate_cfg': {
            'temperature': 0.1,
            'max_tokens': 2000,
        }
    }
    
    # Use built-in tools
    tools = [
        'code_interpreter',
    ]
    
    # Create the assistant
    bot = Assistant(
        llm=llm_cfg,
        function_list=tools,
        name='Final UIUC Qwen Agent',
        description="""I'm an AI assistant powered by Qwen2.5-VL-72B-Instruct running on UIUC infrastructure.

I have access to a code interpreter that can execute Python code with libraries like pandas, matplotlib, numpy, etc.
I can create files, analyze data, perform calculations, and create visualizations.

When you ask me to do something that requires computation or data manipulation, I will use my code interpreter to actually execute the code and provide real results."""
    )
    
    # Chat loop
    conversation_history = []
    while True:
        try:
            query = input('\n💬 You: ').strip()
            
            if not query:
                continue
                
            if query.lower() in ['/quit', '/exit', 'quit', 'exit']:
                print("👋 Goodbye!")
                break
                
            if query.lower() in ['/clear', 'clear']:
                conversation_history = []
                print("🧹 Conversation history cleared!")
                continue
                
            if query.lower() in ['/help', 'help']:
                print("\n🔧 Available Tools:")
                print("• **code_interpreter** - Execute Python code with pandas, matplotlib, numpy, etc.")
                print("\n💡 Example queries:")
                print("• 'Calculate the factorial of 20'")
                print("• 'Create a CSV with random data and plot it'")
                print("• 'Solve the quadratic equation x^2 + 5x + 6 = 0'")
                print("• 'Generate a scatter plot with 100 random points'")
                print("• 'Create a bar chart showing monthly sales data'")
                print("• 'Calculate the mean and standard deviation of [1,2,3,4,5,6,7,8,9,10]'")
                continue
            
            # Add user message to conversation
            conversation_history.append({'role': 'user', 'content': query})
            
            print(f"\n🤖 Processing: {query}")
            print("-" * 60)
            
            # Get response
            try:
                response_text = ''
                for response in bot.run(messages=conversation_history):
                    conversation_history.extend(response)
                    for msg in response:
                        if hasattr(msg, 'content') and msg.content:
                            print(f"🤖 Assistant: {msg.content}")
                            response_text += msg.content
                        elif isinstance(msg, dict) and msg.get('content'):
                            print(f"🤖 Assistant: {msg['content']}")
                            response_text += msg['content']
                
                # Provide feedback on tool usage
                if any(keyword in response_text.lower() for keyword in ['executed', 'result', 'output', 'error']):
                    print("\n✅ Code execution completed!")
                elif 'import' in response_text or 'def ' in response_text:
                    print("\n⚠️  Code provided but may not have been executed")
                    
            except Exception as e:
                print(f"\n❌ Error: {e}")
                print("Please try again or type /quit to exit.")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type /quit to exit.")

def check_server_status():
    """Check if the OpenAI-compatible server is running"""
    try:
        response = requests.get('http://localhost:8000/v1/models', timeout=5)
        if response.status_code == 200:
            print("✅ OpenAI-compatible server is running")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Server not accessible: {e}")
        print("Please make sure the OpenAI-compatible server is running:")
        print("python uiuc_openai_server.py")
        return False

if __name__ == '__main__':
    import sys
    
    # Check server status first
    if not check_server_status():
        print("\n🔧 Please start the OpenAI-compatible server first:")
        print("python uiuc_openai_server.py")
        sys.exit(1)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            test_simple_qwen_agent()
        elif sys.argv[1] == 'chat':
            chat_interface()
        else:
            print("Usage: python final_qwen_agent.py [test|chat]")
    else:
        # Default to chat interface
        chat_interface()
