#!/usr/bin/env python3
"""
Test Native UIUC.chat Tool System
Test the built-in tool capabilities of UIUC.chat
"""

import requests
import json

def test_native_tools():
    """Test native UIUC.chat tool system"""
    
    uiuc_url = "https://uiuc.chat/api/chat-api/chat"
    headers = {'Content-Type': 'application/json'}
    
    # Test requests that should trigger tools
    test_cases = [
        {
            "name": "Code Execution Test",
            "messages": [
                {
                    "role": "user",
                    "content": "Can you create a CSV file with sample sales data (Product, Quantity, Sales) for 5 products, then create a bar chart showing the sales data? Please execute the Python code to actually create these files."
                }
            ]
        },
        {
            "name": "Time Info Test", 
            "messages": [
                {
                    "role": "user",
                    "content": "What is the current date and time?"
                }
            ]
        },
        {
            "name": "Math Calculation Test",
            "messages": [
                {
                    "role": "user", 
                    "content": "Calculate 15 factorial and show me the result by executing Python code."
                }
            ]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        print("=" * 60)
        
        payload = {
            "model": "Qwen/Qwen2.5-VL-72B-Instruct",
            "messages": test_case["messages"],
            "api_key": "uc_0c8d4f0264ec48d0b6a7051b84c496b3",
            "course_name": "modeluse",
            "stream": False,
            "temperature": 0.1,
            "retrieval_only": False
        }
        
        try:
            response = requests.post(uiuc_url, headers=headers, json=payload)
            
            if response.status_code == 200:
                # Parse the response
                response_text = response.text
                print("📥 Raw Response:")
                print("-" * 40)
                print(response_text[:1000] + "..." if len(response_text) > 1000 else response_text)
                print("-" * 40)
                
                # Look for tool execution indicators
                tool_indicators = [
                    "executing",
                    "running",
                    "tool",
                    "function",
                    "code_interpreter",
                    "time_info",
                    "result:",
                    "output:",
                    "execution"
                ]
                
                found_tools = []
                for indicator in tool_indicators:
                    if indicator.lower() in response_text.lower():
                        found_tools.append(indicator)
                
                if found_tools:
                    print(f"✅ Potential tool usage detected: {found_tools}")
                else:
                    print("❌ No tool usage indicators found")
                    
            else:
                print(f"❌ API Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
        
        print("\n" + "="*60)

def test_with_gpt4o():
    """Test with GPT-4o model which has better tool support"""
    
    print("\n🧪 Testing with GPT-4o (Better Tool Support)")
    print("=" * 60)
    
    uiuc_url = "https://uiuc.chat/api/chat-api/chat"
    headers = {'Content-Type': 'application/json'}
    
    payload = {
        "model": "gpt-4o-mini",  # Use GPT-4o for better tool support
        "messages": [
            {
                "role": "user",
                "content": "Please create a CSV file with sample data and then create a plot from that data. Execute the actual Python code to create these files."
            }
        ],
        "openai_key": "YOUR-OPENAI-KEY-HERE",  # This would need a real key
        "api_key": "uc_0c8d4f0264ec48d0b6a7051b84c496b3",
        "course_name": "modeluse", 
        "stream": False,
        "temperature": 0.1,
        "retrieval_only": False
    }
    
    try:
        response = requests.post(uiuc_url, headers=headers, json=payload)
        
        if response.status_code == 200:
            response_text = response.text
            print("📥 GPT-4o Response:")
            print("-" * 40)
            print(response_text[:1000] + "..." if len(response_text) > 1000 else response_text)
            print("-" * 40)
        else:
            print(f"❌ API Error {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == '__main__':
    test_native_tools()
    # test_with_gpt4o()  # Uncomment if you have OpenAI key
