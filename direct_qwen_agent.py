#!/usr/bin/env python3
"""
Direct Qwen-Agent Implementation
Works directly with UIUC API using custom LLM class
"""

import json
import requests
import time
import re
from typing import Dict, Any, List, Iterator, Optional
from qwen_agent.llm.base import BaseChatModel
from qwen_agent.agents import Assistant
from qwen_agent.tools.base import BaseTool, register_tool

class UIUCQwenLLM(BaseChatModel):
    """Custom LLM class that works directly with UIUC API"""

    def __init__(self, cfg: Optional[Dict] = None):
        super().__init__(cfg)
        self.model = cfg.get('model', 'Qwen/Qwen2.5-VL-72B-Instruct')
        self.api_key = cfg.get('api_key', 'uc_0c8d4f0264ec48d0b6a7051b84c496b3')
        self.uiuc_url = "https://uiuc.chat/api/chat-api/chat"
        self.generate_cfg = cfg.get('generate_cfg', {})

    def _chat_no_stream(self, messages: List[Dict], **kwargs) -> List[Dict]:
        """Non-streaming chat implementation"""
        response_text = self._call_uiuc_api(messages)
        return [{"role": "assistant", "content": response_text}]

    def _chat_stream(self, messages: List[Dict], **kwargs) -> Iterator[List[Dict]]:
        """Streaming chat implementation"""
        response_text = self._call_uiuc_api(messages)
        yield [{"role": "assistant", "content": response_text}]

    def _chat_with_functions(self, messages: List[Dict], functions: List[Dict], **kwargs) -> Iterator[List[Dict]]:
        """Chat with functions implementation"""
        return self.chat(messages, functions, **kwargs)
        
    def _call_uiuc_api(self, messages: List[Dict], functions: Optional[List[Dict]] = None) -> str:
        """Call UIUC API directly"""
        
        # Prepare the request
        uiuc_request = {
            "model": self.model,
            "messages": messages,
            "api_key": self.api_key,
            "course_name": "modeluse",
            "stream": False,
            "temperature": self.generate_cfg.get('temperature', 0.1),
            "retrieval_only": False
        }
        
        # If functions are provided, add them to system message
        if functions:
            function_descriptions = []
            for func in functions:
                func_desc = f"- **{func['name']}**: {func['description']}"
                if 'parameters' in func and func['parameters']:
                    params = []
                    for param in func['parameters']:
                        if isinstance(param, dict):
                            param_desc = f"{param.get('name', 'unknown')} ({param.get('type', 'unknown')})"
                            if param.get('required'):
                                param_desc += " [required]"
                            param_desc += f": {param.get('description', 'No description')}"
                            params.append(param_desc)
                    if params:
                        func_desc += f"\n  Parameters: {', '.join(params)}"
                function_descriptions.append(func_desc)
            
            system_msg = f"""You are a helpful AI assistant with access to the following tools:

{chr(10).join(function_descriptions)}

When you need to use a tool, respond with a function call in this exact JSON format:
{{"function_call": {{"name": "tool_name", "arguments": {{"param": "value"}}}}}}

Always use tools when appropriate for the user's request. Execute the tools to provide actual results, don't just give code examples."""
            
            # Add system message at the beginning
            uiuc_request['messages'].insert(0, {
                "role": "system", 
                "content": system_msg
            })
        
        try:
            response = requests.post(
                self.uiuc_url,
                headers={'Content-Type': 'application/json'},
                json=uiuc_request,
                timeout=60
            )
            response.raise_for_status()
            return response.text.strip()
        except Exception as e:
            return f"Error calling UIUC API: {e}"
    
    def chat(self, messages: List[Dict], functions: Optional[List[Dict]] = None, **kwargs) -> Iterator[List[Dict]]:
        """Chat method required by Qwen-Agent"""
        
        # Call UIUC API
        response_text = self._call_uiuc_api(messages, functions)
        
        # Check if response contains function call
        function_call_pattern = r'\{"function_call":\s*\{[^}]+\}\}'
        match = re.search(function_call_pattern, response_text)
        
        if match:
            try:
                function_call_json = json.loads(match.group())
                function_call = function_call_json.get('function_call', {})
                
                # Remove the function call JSON from content
                clean_content = re.sub(function_call_pattern, '', response_text).strip()
                
                yield [{
                    "role": "assistant",
                    "content": clean_content if clean_content else "",
                    "function_call": function_call
                }]
            except json.JSONDecodeError:
                # If parsing fails, treat as regular content
                yield [{"role": "assistant", "content": response_text}]
        else:
            yield [{"role": "assistant", "content": response_text}]

# Custom Tools
@register_tool('current_time')
class CurrentTimeTool(BaseTool):
    """Get current date and time information"""
    description = 'Get current date and time'
    parameters = []

    def call(self, params: str = "", **kwargs) -> str:
        from datetime import datetime
        now = datetime.now()
        return f"Current date and time: {now.strftime('%Y-%m-%d %H:%M:%S')}\nDay of week: {now.strftime('%A')}\nTimezone: {now.astimezone().tzname()}"



def create_direct_agent():
    """Create agent with direct UIUC API integration"""
    
    # Configure custom LLM
    llm_cfg = {
        'model': 'Qwen/Qwen2.5-VL-72B-Instruct',
        'api_key': 'uc_0c8d4f0264ec48d0b6a7051b84c496b3',
        'generate_cfg': {
            'temperature': 0.1,
            'max_tokens': 2000,
        }
    }
    
    # Create custom LLM instance
    llm = UIUCQwenLLM(llm_cfg)
    
    # Define available tools
    tools = [
        'code_interpreter',  # Built-in Qwen-Agent tool
        'current_time',      # Custom time tool
    ]
    
    # Create the assistant
    bot = Assistant(
        llm=llm,
        function_list=tools,
        name='Direct UIUC Qwen Agent',
        description="""I'm an AI assistant powered by Qwen2.5-VL-72B-Instruct running on UIUC infrastructure.

🔧 **My Capabilities:**
• **Code Interpreter** - Execute Python code with pandas, matplotlib, numpy support
• **Current Time** - Get current date and time information
• **Data Analysis** - Analyze data and create visualizations
• **Problem Solving** - Break down complex tasks into executable steps

I can actually execute code and use tools, not just provide examples!"""
    )
    
    return bot

def test_direct_agent():
    """Test the direct agent"""
    print("🚀 Testing Direct Qwen-Agent...")
    print("=" * 80)
    
    # Initialize the agent
    bot = create_direct_agent()
    
    # Test queries
    test_queries = [
        "What is the current time and date?",
        "Create a CSV with sample data and then create plots on that"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test {i}: {query}")
        print("-" * 60)
        
        # Run the query
        messages = [{'role': 'user', 'content': query}]
        
        for response in bot.run(messages=messages):
            for msg in response:
                if msg.get('role') == 'assistant':
                    print(f"🤖 Assistant: {msg.get('content', '')}")
                elif msg.get('role') == 'function':
                    print(f"🔧 Tool Result: {msg.get('content', '')}")
        
        print("-" * 60)
    
    print("\n✅ All tests completed!")

def chat_interface():
    """Interactive chat interface"""
    print("🤖 Direct Qwen-Agent Chat Interface")
    print("=" * 60)
    print("🔧 **Capabilities:**")
    print("• Code Interpreter - Execute Python code with full library support")
    print("• Time Info - Get current date and time")
    print("• Data Analysis - Analyze and visualize data")
    print("=" * 60)
    print("Commands: /quit (exit), /help (show help)")
    print("=" * 60)
    
    # Initialize the agent
    bot = create_direct_agent()
    
    # Chat loop
    messages = []
    while True:
        try:
            query = input('\n💬 You: ').strip()
            
            if not query:
                continue
                
            if query.lower() in ['/quit', '/exit', 'quit', 'exit']:
                print("👋 Goodbye!")
                break
                
            if query.lower() in ['/help', 'help']:
                print("\n🔧 Available Tools:")
                print("• **code_interpreter** - Execute Python code")
                print("• **current_time** - Get current date and time")
                print("\n💡 Example queries:")
                print("• 'What is the current time?'")
                print("• 'Create a CSV with sales data and plot it'")
                print("• 'Calculate the factorial of 10'")
                continue
            
            # Add user message
            messages.append({'role': 'user', 'content': query})
            
            print(f"\n🤖 Processing: {query}")
            print("-" * 60)
            
            # Get response
            for response in bot.run(messages=messages):
                messages.extend(response)
                for msg in response:
                    if msg.get('role') == 'assistant':
                        print(f"🤖 Assistant: {msg.get('content', '')}")
                    elif msg.get('role') == 'function':
                        print(f"🔧 Tool Result: {msg.get('content', '')}")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type /quit to exit.")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            test_direct_agent()
        elif sys.argv[1] == 'chat':
            chat_interface()
        else:
            print("Usage: python direct_qwen_agent.py [test|chat]")
    else:
        # Default to chat interface
        chat_interface()
