#!/usr/bin/env python3
"""
Test Function Calling Implementation
Direct test to see what the UIUC model outputs when prompted for function calls
"""

import requests
import json

def test_direct_function_call():
    """Test direct function calling with UIUC API"""
    
    # Direct UIUC API call with function calling prompt
    uiuc_url = "https://uiuc.chat/api/chat-api/chat"
    headers = {'Content-Type': 'application/json'}
    
    # Test with explicit function calling instructions
    messages = [
        {
            "role": "system",
            "content": """You are a helpful assistant with access to the following tools:

- **code_interpreter**: Execute Python code with pandas, matplotlib, numpy support
  Parameters:
  - code (string): The Python code to execute

IMPORTANT: When you need to use a tool, you MUST respond with a function call in this EXACT format:
<function_call>
{"name": "code_interpreter", "arguments": {"code": "your_python_code_here"}}
</function_call>

For example, if asked to calculate 15 factorial, respond with:
<function_call>
{"name": "code_interpreter", "arguments": {"code": "import math\\nresult = math.factorial(15)\\nprint(f'15! = {result}')"}}
</function_call>

Do NOT provide code examples - actually use the function call format!"""
        },
        {
            "role": "user", 
            "content": "What is 15 factorial? Calculate it using Python."
        }
    ]
    
    payload = {
        "model": "Qwen/Qwen2.5-VL-72B-Instruct",
        "messages": messages,
        "api_key": "uc_0c8d4f0264ec48d0b6a7051b84c496b3",
        "course_name": "modeluse",
        "stream": False,
        "temperature": 0.1,
        "retrieval_only": False
    }
    
    print("🧪 Testing Direct Function Calling...")
    print("=" * 60)
    print("📤 Sending request to UIUC API...")
    
    try:
        response = requests.post(uiuc_url, headers=headers, json=payload)
        
        if response.status_code == 200:
            full_response = ""
            for chunk in response.iter_lines():
                if chunk:
                    full_response += chunk.decode()
            
            print("📥 Raw UIUC Response:")
            print("-" * 40)
            print(full_response)
            print("-" * 40)
            
            # Check for function call patterns
            import re
            patterns = [
                r'<function_call>\s*(\{.*?\})\s*</function_call>',
                r'```json\s*(\{[^}]*"name"[^}]*\})\s*```',
                r'\{"name":\s*"([^"]+)",\s*"arguments":\s*(\{.*?\})\}',
            ]
            
            found_function_call = False
            for pattern in patterns:
                matches = re.findall(pattern, full_response, re.DOTALL)
                if matches:
                    print(f"✅ Found function call pattern: {pattern}")
                    print(f"📋 Matches: {matches}")
                    found_function_call = True
                    break
            
            if not found_function_call:
                print("❌ No function call patterns found in response")
                print("🔍 Looking for any JSON-like structures...")
                json_pattern = r'\{[^}]*\}'
                json_matches = re.findall(json_pattern, full_response)
                if json_matches:
                    print(f"📋 JSON structures found: {json_matches}")
                else:
                    print("❌ No JSON structures found")
            
        else:
            print(f"❌ API Error {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_openai_server():
    """Test our OpenAI-compatible server"""
    
    print("\n🧪 Testing OpenAI-Compatible Server...")
    print("=" * 60)
    
    payload = {
        "model": "Qwen/Qwen2.5-VL-72B-Instruct",
        "messages": [
            {"role": "user", "content": "What is 15 factorial? Calculate it using Python."}
        ],
        "tools": [
            {
                "type": "function",
                "function": {
                    "name": "code_interpreter",
                    "description": "Execute Python code with pandas, matplotlib, numpy support",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "code": {
                                "type": "string",
                                "description": "The Python code to execute"
                            }
                        },
                        "required": ["code"]
                    }
                }
            }
        ],
        "temperature": 0.1
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json"},
            json=payload
        )
        
        if response.status_code == 200:
            result = response.json()
            print("📥 OpenAI Server Response:")
            print("-" * 40)
            print(json.dumps(result, indent=2))
            print("-" * 40)
            
            # Check if tool_calls are present
            choices = result.get('choices', [])
            if choices:
                message = choices[0].get('message', {})
                tool_calls = message.get('tool_calls', [])
                if tool_calls:
                    print("✅ Tool calls found!")
                    for call in tool_calls:
                        print(f"🔧 Tool: {call}")
                else:
                    print("❌ No tool calls found in response")
            
        else:
            print(f"❌ Server Error {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == '__main__':
    test_direct_function_call()
    test_openai_server()
