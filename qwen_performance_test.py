#!/usr/bin/env python3
"""
Comprehensive performance testing for UIUC hosted Qwen2.5-VL-72B-Instruct model
Measures tokens per second, latency, and throughput across different query types
"""

import requests
import json
import time
import statistics
from typing import List, Dict, Tuple
import re

class QwenPerformanceTester:
    def __init__(self):
        self.url = "https://uiuc.chat/api/chat-api/chat"
        self.headers = {'Content-Type': 'application/json'}
        self.api_key = "uc_0c8d4f0264ec48d0b6a7051b84c496b3"
        self.model = "Qwen/Qwen2.5-VL-72B-Instruct"
        
        # Test queries of different lengths and complexities
        self.test_queries = [
            # Short queries
            "What is 2+2?",
            "Hello, how are you?",
            "What's the weather like?",
            
            # Medium queries
            "Explain the concept of machine learning in simple terms.",
            "What are the main differences between Python and JavaScript?",
            "How does photosynthesis work in plants?",
            
            # Long queries
            "Write a detailed explanation of quantum computing, including its principles, current applications, and potential future impact on technology and society.",
            "Explain the history of artificial intelligence from its inception to modern deep learning, including key milestones and breakthrough technologies.",
            "Describe the process of protein folding, its importance in biology, and how computational methods are being used to predict protein structures.",
            
            # Creative queries
            "Write a short story about a robot learning to paint.",
            "Create a recipe for a fictional dish from another planet.",
            "Compose a haiku about the ocean.",
            
            # Technical queries
            "Implement a binary search algorithm in Python with comments.",
            "Explain the differences between SQL and NoSQL databases with examples.",
            "What are the key principles of RESTful API design?"
        ]
    
    def estimate_tokens(self, text: str) -> int:
        """Rough estimation of tokens (approximately 4 characters per token)"""
        return len(text) // 4
    
    def measure_single_request(self, query: str, test_name: str = "") -> Dict:
        """Measure performance of a single request"""
        data = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": query}
            ],
            "api_key": self.api_key,
            "course_name": "modeluse",
            "stream": True,
            "temperature": 0.1,
            "retrieval_only": False
        }
        
        start_time = time.time()
        first_token_time = None
        response_text = ""
        
        try:
            response = requests.post(self.url, headers=self.headers, json=data)
            
            if response.status_code != 200:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
            
            for chunk in response.iter_lines():
                if chunk:
                    if first_token_time is None:
                        first_token_time = time.time()
                    
                    chunk_text = chunk.decode()
                    response_text += chunk_text
            
            end_time = time.time()
            
            # Calculate metrics
            total_time = end_time - start_time
            time_to_first_token = first_token_time - start_time if first_token_time else 0
            
            input_tokens = self.estimate_tokens(query)
            output_tokens = self.estimate_tokens(response_text)
            total_tokens = input_tokens + output_tokens
            
            tokens_per_second = output_tokens / total_time if total_time > 0 else 0
            
            return {
                "test_name": test_name,
                "query": query[:50] + "..." if len(query) > 50 else query,
                "response_length": len(response_text),
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": total_tokens,
                "total_time": total_time,
                "time_to_first_token": time_to_first_token,
                "tokens_per_second": tokens_per_second,
                "response_preview": response_text[:100] + "..." if len(response_text) > 100 else response_text
            }
            
        except Exception as e:
            return {"error": f"Request failed: {e}"}
    
    def run_performance_tests(self, num_iterations: int = 3) -> List[Dict]:
        """Run comprehensive performance tests"""
        print(f"🚀 Starting Qwen Performance Tests ({num_iterations} iterations per query)")
        print("=" * 80)
        
        all_results = []
        
        for i, query in enumerate(self.test_queries, 1):
            print(f"\nTest {i}/{len(self.test_queries)}: {query[:50]}...")
            
            test_results = []
            for iteration in range(num_iterations):
                print(f"  Iteration {iteration + 1}/{num_iterations}...", end=" ", flush=True)
                
                result = self.measure_single_request(query, f"Test_{i}")
                
                if "error" in result:
                    print(f"❌ Error: {result['error']}")
                    continue
                
                test_results.append(result)
                print(f"✅ {result['tokens_per_second']:.1f} tok/s")
            
            if test_results:
                # Calculate averages for this query
                avg_result = self.calculate_averages(test_results, query)
                all_results.append(avg_result)
                
                print(f"  📊 Average: {avg_result['avg_tokens_per_second']:.1f} tok/s, "
                      f"Latency: {avg_result['avg_total_time']:.2f}s")
        
        return all_results
    
    def calculate_averages(self, results: List[Dict], query: str) -> Dict:
        """Calculate average metrics from multiple test runs"""
        if not results:
            return {}
        
        metrics = {
            'tokens_per_second': [r['tokens_per_second'] for r in results],
            'total_time': [r['total_time'] for r in results],
            'time_to_first_token': [r['time_to_first_token'] for r in results],
            'output_tokens': [r['output_tokens'] for r in results]
        }
        
        return {
            'query': query[:50] + "..." if len(query) > 50 else query,
            'num_tests': len(results),
            'avg_tokens_per_second': statistics.mean(metrics['tokens_per_second']),
            'min_tokens_per_second': min(metrics['tokens_per_second']),
            'max_tokens_per_second': max(metrics['tokens_per_second']),
            'avg_total_time': statistics.mean(metrics['total_time']),
            'avg_time_to_first_token': statistics.mean(metrics['time_to_first_token']),
            'avg_output_tokens': statistics.mean(metrics['output_tokens']),
            'response_preview': results[0]['response_preview']
        }
    
    def print_summary_report(self, results: List[Dict]):
        """Print a comprehensive summary report"""
        if not results:
            print("❌ No successful tests to report")
            return
        
        print("\n" + "=" * 80)
        print("📈 PERFORMANCE SUMMARY REPORT")
        print("=" * 80)
        
        # Overall statistics
        all_tps = [r['avg_tokens_per_second'] for r in results]
        all_latency = [r['avg_total_time'] for r in results]
        all_ttft = [r['avg_time_to_first_token'] for r in results]
        
        print(f"\n🎯 OVERALL METRICS:")
        print(f"   Average Tokens/Second: {statistics.mean(all_tps):.2f}")
        print(f"   Min Tokens/Second: {min(all_tps):.2f}")
        print(f"   Max Tokens/Second: {max(all_tps):.2f}")
        print(f"   Average Latency: {statistics.mean(all_latency):.2f}s")
        print(f"   Average Time to First Token: {statistics.mean(all_ttft):.3f}s")
        
        # Detailed results
        print(f"\n📋 DETAILED RESULTS:")
        print(f"{'Query':<50} {'Tok/s':<8} {'Latency':<8} {'TTFT':<8} {'Tokens':<8}")
        print("-" * 90)
        
        for result in results:
            query_short = result['query'][:45] + "..." if len(result['query']) > 45 else result['query']
            print(f"{query_short:<50} "
                  f"{result['avg_tokens_per_second']:<8.1f} "
                  f"{result['avg_total_time']:<8.2f} "
                  f"{result['avg_time_to_first_token']:<8.3f} "
                  f"{result['avg_output_tokens']:<8.0f}")
        
        print("\n💡 Legend: Tok/s = Tokens per second, TTFT = Time to first token")

def main():
    """Run the performance testing suite"""
    tester = QwenPerformanceTester()
    
    print("🔧 Qwen2.5-VL-72B Performance Testing Suite")
    print("This will test various query types and measure performance metrics")
    
    # Run tests
    results = tester.run_performance_tests(num_iterations=2)
    
    # Print summary
    tester.print_summary_report(results)
    
    print(f"\n✅ Performance testing complete! Tested {len(results)} different queries.")

if __name__ == "__main__":
    main()
