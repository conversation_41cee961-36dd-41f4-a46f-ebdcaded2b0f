#!/usr/bin/env python3
"""
OpenAI-Compatible API Server for UIUC Qwen Model
Converts UIUC API format to OpenAI format for Qwen-Agent compatibility
"""

from flask import Flask, request, jsonify, Response
import requests
import json
import uuid
import re
import subprocess
import tempfile
import os
import io
import sys
from contextlib import redirect_stdout, redirect_stderr
from datetime import datetime
from typing import Dict, List, Any, Optional, Iterator
import threading
import time

app = Flask(__name__)

class FunctionExecutor:
    """Executes function calls for built-in tools"""

    def __init__(self):
        self.working_directory = tempfile.mkdtemp()
        print(f"📁 Function executor working directory: {self.working_directory}")

    def execute_function(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a function call and return the result"""
        try:
            if function_name == "code_interpreter":
                return self._execute_code_interpreter(arguments)
            elif function_name == "time_info":
                return self._execute_time_info(arguments)
            else:
                return {
                    "error": f"Unknown function: {function_name}",
                    "success": False
                }
        except Exception as e:
            return {
                "error": f"Function execution failed: {str(e)}",
                "success": False
            }

    def _execute_code_interpreter(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Python code in a safe environment"""
        code = arguments.get("code", "")
        if not code:
            return {"error": "No code provided", "success": False}

        # Capture stdout and stderr
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()

        try:
            # Change to working directory
            old_cwd = os.getcwd()
            os.chdir(self.working_directory)

            # Execute the code
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                exec(code, {"__name__": "__main__"})

            stdout_output = stdout_capture.getvalue()
            stderr_output = stderr_capture.getvalue()

            # List files created
            files_created = []
            for file in os.listdir(self.working_directory):
                if os.path.isfile(os.path.join(self.working_directory, file)):
                    files_created.append(file)

            result = {
                "success": True,
                "stdout": stdout_output,
                "stderr": stderr_output,
                "files_created": files_created,
                "working_directory": self.working_directory
            }

            return result

        except Exception as e:
            return {
                "error": f"Code execution failed: {str(e)}",
                "success": False,
                "stderr": stderr_capture.getvalue()
            }
        finally:
            os.chdir(old_cwd)

    def _execute_time_info(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Get current time and date information"""
        now = datetime.now()
        return {
            "success": True,
            "current_time": now.strftime("%H:%M:%S"),
            "current_date": now.strftime("%Y-%m-%d"),
            "current_datetime": now.strftime("%Y-%m-%d %H:%M:%S"),
            "day_of_week": now.strftime("%A"),
            "timezone": str(now.astimezone().tzinfo)
        }

class UIUCToOpenAIConverter:
    """Converts UIUC API calls to OpenAI-compatible format"""
    
    def __init__(self):
        self.uiuc_url = "https://uiuc.chat/api/chat-api/chat"
        self.uiuc_headers = {'Content-Type': 'application/json'}
        self.model_name = "Qwen/Qwen2.5-VL-72B-Instruct"
        self.api_key = "uc_0c8d4f0264ec48d0b6a7051b84c496b3"
        self.function_executor = FunctionExecutor()

        # Function call patterns for parsing responses
        self.function_call_patterns = [
            r'<function_call>\s*(\{.*?\})\s*</function_call>',
            r'```json\s*(\{[^}]*"function"[^}]*\})\s*```',
            r'\{"function":\s*"([^"]+)",\s*"arguments":\s*(\{.*?\})\}',
            r'function_call:\s*(\{.*?\})',
        ]
    
    def _prepare_uiuc_request(self, openai_request: Dict[str, Any]) -> Dict[str, Any]:
        """Convert OpenAI request format to UIUC format"""
        messages = openai_request.get('messages', [])
        tools = openai_request.get('tools', [])

        # Ensure non-empty messages
        processed_messages = []
        for msg in messages:
            content = msg.get('content', '')
            if not content or not content.strip():
                if msg.get('role') == 'system':
                    content = "You are a helpful assistant."
                elif msg.get('role') == 'user':
                    content = "Hello"
                else:
                    content = "I understand."

            processed_messages.append({
                "role": msg.get('role', 'user'),
                "content": content
            })

        # If tools are provided, add them to the system message for function calling
        if tools:
            tool_descriptions = []
            for tool in tools:
                func = tool.get('function', {})
                name = func.get('name', 'unknown')
                description = func.get('description', 'No description')

                # Add parameter information if available
                params_info = ""
                if 'parameters' in func and func['parameters']:
                    params = func['parameters']
                    if 'properties' in params:
                        param_list = []
                        for param_name, param_info in params['properties'].items():
                            param_type = param_info.get('type', 'unknown')
                            param_desc = param_info.get('description', 'No description')
                            param_list.append(f"  - {param_name} ({param_type}): {param_desc}")
                        if param_list:
                            params_info = f"\n  Parameters:\n" + "\n".join(param_list)

                tool_descriptions.append(f"- **{name}**: {description}{params_info}")

            system_msg = f"""You are a helpful assistant with access to the following tools:

{chr(10).join(tool_descriptions)}

When the user asks for:
- Code execution, calculations, data analysis, plotting, CSV creation → Provide Python code in ```python code blocks
- Current time, date information → I will automatically get the current time for you

IMPORTANT: When providing Python code, always use ```python code blocks. The code will be automatically executed for you.

Examples:
- For calculations: ```python
import math
result = math.factorial(15)
print(f"15! = {{result}}")
```

- For data analysis: ```python
import pandas as pd
import matplotlib.pyplot as plt
# Create sample data
data = {{'Product': ['A', 'B', 'C'], 'Sales': [100, 200, 150]}}
df = pd.DataFrame(data)
print(df)
# Create plot
plt.bar(df['Product'], df['Sales'])
plt.title('Sales by Product')
plt.savefig('sales_chart.png')
plt.show()
```

Always provide working, complete Python code that accomplishes the user's request."""

            # Add system message at the beginning
            processed_messages.insert(0, {
                "role": "system",
                "content": system_msg
            })

        return {
            "model": self.model_name,
            "messages": processed_messages,
            "api_key": self.api_key,
            "course_name": "modeluse",
            "stream": openai_request.get('stream', False),
            "temperature": openai_request.get('temperature', 0.1),
            "retrieval_only": False
        }
    
    def _call_uiuc_api(self, uiuc_request: Dict[str, Any]) -> str:
        """Call UIUC API and return response"""
        try:
            response = requests.post(self.uiuc_url, headers=self.uiuc_headers, json=uiuc_request)
            
            if response.status_code == 200:
                full_response = ""
                for chunk in response.iter_lines():
                    if chunk:
                        full_response += chunk.decode()
                return full_response
            else:
                return f"API Error {response.status_code}: {response.text}"
        except Exception as e:
            return f"Request failed: {e}"

    def _parse_function_calls(self, content: str) -> List[Dict[str, Any]]:
        """Parse function calls from the response content"""
        function_calls = []

        # First try explicit function call format
        pattern = r'<function_call>\s*(\{.*?\})\s*</function_call>'
        matches = re.findall(pattern, content, re.DOTALL)

        for match in matches:
            try:
                func_data = json.loads(match)
                function_calls.append({
                    "id": f"call_{uuid.uuid4().hex[:8]}",
                    "type": "function",
                    "function": {
                        "name": func_data.get("name", ""),
                        "arguments": json.dumps(func_data.get("arguments", {}))
                    }
                })
            except json.JSONDecodeError:
                continue

        # If no explicit function calls found, look for code blocks and auto-execute them
        if not function_calls:
            # Look for Python code blocks
            code_pattern = r'```python\s*(.*?)\s*```'
            code_matches = re.findall(code_pattern, content, re.DOTALL)

            for code in code_matches:
                if code.strip():
                    function_calls.append({
                        "id": f"call_{uuid.uuid4().hex[:8]}",
                        "type": "function",
                        "function": {
                            "name": "code_interpreter",
                            "arguments": json.dumps({"code": code.strip()})
                        }
                    })

            # Also look for time-related queries
            time_keywords = ['time', 'date', 'today', 'now', 'current']
            if any(keyword in content.lower() for keyword in time_keywords) and 'factorial' not in content.lower():
                function_calls.append({
                    "id": f"call_{uuid.uuid4().hex[:8]}",
                    "type": "function",
                    "function": {
                        "name": "time_info",
                        "arguments": json.dumps({})
                    }
                })

        return function_calls

    def _execute_function_calls(self, function_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute function calls and return results"""
        results = []

        for call in function_calls:
            call_id = call["id"]
            function_name = call["function"]["name"]

            try:
                arguments = json.loads(call["function"]["arguments"])
            except json.JSONDecodeError:
                arguments = {}

            # Execute the function
            result = self.function_executor.execute_function(function_name, arguments)

            # Format result for OpenAI
            results.append({
                "role": "tool",
                "tool_call_id": call_id,
                "content": json.dumps(result)
            })

        return results

    def _create_openai_response(self, content: str, request_id: str, model: str,
                               finish_reason: str = "stop") -> Dict[str, Any]:
        """Create OpenAI-compatible response format with function calling support"""

        # Parse function calls from content
        function_calls = self._parse_function_calls(content)

        # Remove function call tags from content for clean display
        clean_content = re.sub(r'<function_call>.*?</function_call>', '', content, flags=re.DOTALL).strip()

        if function_calls:
            # If function calls found, create response with tool_calls
            message = {
                "role": "assistant",
                "content": clean_content if clean_content else None,
                "tool_calls": function_calls
            }
            finish_reason = "tool_calls"
        else:
            # Regular response without function calls
            message = {
                "role": "assistant",
                "content": content
            }

        return {
            "id": f"chatcmpl-{request_id}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": model,
            "choices": [{
                "index": 0,
                "message": message,
                "finish_reason": finish_reason
            }],
            "usage": {
                "prompt_tokens": 0,  # UIUC API doesn't provide token counts
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }
    
    def _create_streaming_chunk(self, content: str, request_id: str, model: str, 
                               is_final: bool = False) -> str:
        """Create OpenAI-compatible streaming chunk"""
        chunk_data = {
            "id": f"chatcmpl-{request_id}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": model,
            "choices": [{
                "index": 0,
                "delta": {
                    "content": content if not is_final else None
                },
                "finish_reason": "stop" if is_final else None
            }]
        }
        
        return f"data: {json.dumps(chunk_data)}\n\n"

converter = UIUCToOpenAIConverter()

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """OpenAI-compatible chat completions endpoint"""
    try:
        openai_request = request.json
        request_id = str(uuid.uuid4())[:8]
        model = openai_request.get('model', converter.model_name)
        
        # Convert to UIUC format
        uiuc_request = converter._prepare_uiuc_request(openai_request)
        
        # Handle streaming vs non-streaming
        if openai_request.get('stream', False):
            def generate():
                # Call UIUC API
                content = converter._call_uiuc_api(uiuc_request)
                
                # Send content in chunks
                chunk_size = 50
                for i in range(0, len(content), chunk_size):
                    chunk = content[i:i+chunk_size]
                    yield converter._create_streaming_chunk(chunk, request_id, model)
                    time.sleep(0.01)  # Small delay for streaming effect
                
                # Send final chunk
                yield converter._create_streaming_chunk("", request_id, model, is_final=True)
                yield "data: [DONE]\n\n"
            
            return Response(generate(), mimetype='text/plain')
        else:
            # Non-streaming response with function calling support
            content = converter._call_uiuc_api(uiuc_request)

            # Check if function calls are present
            function_calls = converter._parse_function_calls(content)

            if function_calls:
                # Execute function calls
                function_results = converter._execute_function_calls(function_calls)

                # Create response with tool calls
                response = converter._create_openai_response(content, request_id, model)

                # If this is a function calling request, we need to handle the conversation flow
                tools = openai_request.get('tools', [])
                if tools:
                    # Add function results to conversation and get final response
                    messages = openai_request.get('messages', [])

                    # Add assistant message with tool calls
                    messages.append(response['choices'][0]['message'])

                    # Add tool results
                    messages.extend(function_results)

                    # Make another call to get the final response
                    final_request = {
                        **openai_request,
                        'messages': messages,
                        'tools': None  # Remove tools for final response
                    }

                    final_uiuc_request = converter._prepare_uiuc_request(final_request)
                    final_content = converter._call_uiuc_api(final_uiuc_request)
                    final_response = converter._create_openai_response(final_content, request_id, model)

                    return jsonify(final_response)

                return jsonify(response)
            else:
                # Regular response without function calls
                response = converter._create_openai_response(content, request_id, model)
                return jsonify(response)
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/v1/models', methods=['GET'])
def list_models():
    """List available models"""
    return jsonify({
        "object": "list",
        "data": [{
            "id": converter.model_name,
            "object": "model",
            "created": int(time.time()),
            "owned_by": "uiuc"
        }]
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy"})

if __name__ == '__main__':
    print("🚀 Starting UIUC to OpenAI API Converter")
    print(f"📡 Converting UIUC API: {converter.uiuc_url}")
    print(f"🔗 OpenAI-compatible endpoint: http://localhost:8000/v1")
    print(f"🤖 Model: {converter.model_name}")
    print("=" * 60)
    
    app.run(host='0.0.0.0', port=8000, debug=False)
