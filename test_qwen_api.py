#!/usr/bin/env python3
"""
Test script for the UIUC hosted Qwen2.5-VL-72B-Instruct model API
"""

import requests
import json

def test_qwen_api():
    """Test the Qwen API with a simple query"""
    
    url = "https://uiuc.chat/api/chat-api/chat"
    headers = {
        'Content-Type': 'application/json'
    }
    
    data = {
        "model": "Qwen/Qwen2.5-VL-72B-Instruct",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user",
                "content": "Hello! Can you tell me what model you are and briefly describe your capabilities?"
            }
        ],
        "api_key": "uc_0c8d4f0264ec48d0b6a7051b84c496b3",
        "course_name": "modeluse",
        "stream": True,
        "temperature": 0.1,
        "retrieval_only": False
    }
    
    print("Testing Qwen API connection...")
    print(f"URL: {url}")
    print(f"Model: {data['model']}")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            print("\nAPI Response:")
            print("-" * 30)
            for chunk in response.iter_lines():
                if chunk:
                    print(chunk.decode())
        else:
            print(f"Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    test_qwen_api()
