#!/usr/bin/env python3
"""
Working Qwen-Agent Implementation
Uses direct UIUC API integration with proper function calling support
"""

import os
import json
import requests
from qwen_agent.llm import get_chat_model
from qwen_agent.agents import Assistant
from qwen_agent.tools.base import BaseTool, register_tool
from qwen_agent.utils.output_beautify import typewriter_print
from typing import Dict, Any

# Custom Tools for demonstration
@register_tool('code_executor')
class CodeExecutorTool(BaseTool):
    """Execute Python code and return results"""
    description = 'Execute Python code and return the output'
    parameters = [{
        'name': 'code',
        'type': 'string',
        'description': 'Python code to execute',
        'required': True
    }]

    def call(self, params: str, **kwargs) -> str:
        try:
            import json5
            import io
            import sys
            from contextlib import redirect_stdout, redirect_stderr
            
            code = json5.loads(params)['code']
            
            # Capture output
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()
            
            # Create a safe execution environment
            exec_globals = {
                '__builtins__': __builtins__,
                'print': print,
                'len': len,
                'range': range,
                'list': list,
                'dict': dict,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
                'sum': sum,
                'max': max,
                'min': min,
                'abs': abs,
                'round': round,
            }
            
            # Add common libraries
            try:
                import math
                import datetime
                import csv
                import os
                import pandas as pd
                import matplotlib.pyplot as plt
                import numpy as np
                
                exec_globals.update({
                    'math': math,
                    'datetime': datetime,
                    'csv': csv,
                    'os': os,
                    'pd': pd,
                    'plt': plt,
                    'np': np
                })
            except ImportError:
                pass
            
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                exec(code, exec_globals)
            
            stdout_result = stdout_capture.getvalue()
            stderr_result = stderr_capture.getvalue()
            
            result = {
                'code': code,
                'stdout': stdout_result,
                'stderr': stderr_result,
                'success': True
            }
            
            if stderr_result:
                result['success'] = False
                
            return json5.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            return json5.dumps({
                'code': code if 'code' in locals() else 'unknown',
                'error': str(e),
                'success': False
            }, ensure_ascii=False)

@register_tool('csv_creator')
class CSVCreatorTool(BaseTool):
    """Create CSV files with sample data"""
    description = 'Create a CSV file with sample data'
    parameters = [{
        'name': 'filename',
        'type': 'string',
        'description': 'Name of the CSV file to create',
        'required': True
    }, {
        'name': 'data_type',
        'type': 'string',
        'description': 'Type of sample data: "sales", "weather", "stock", "custom"',
        'required': True
    }]

    def call(self, params: str, **kwargs) -> str:
        try:
            import json5
            import csv
            import random
            from datetime import datetime, timedelta
            
            params_dict = json5.loads(params)
            filename = params_dict['filename']
            data_type = params_dict['data_type']
            
            # Generate sample data based on type
            if data_type == "sales":
                data = [
                    ['Date', 'Product', 'Sales', 'Revenue'],
                    ['2024-01-01', 'Product A', 120, 2400],
                    ['2024-01-02', 'Product B', 85, 1700],
                    ['2024-01-03', 'Product A', 95, 1900],
                    ['2024-01-04', 'Product C', 150, 3000],
                    ['2024-01-05', 'Product B', 110, 2200]
                ]
            elif data_type == "weather":
                data = [
                    ['Date', 'Temperature', 'Humidity', 'Rainfall'],
                    ['2024-01-01', 22.5, 65, 0.0],
                    ['2024-01-02', 24.1, 70, 2.5],
                    ['2024-01-03', 19.8, 80, 15.2],
                    ['2024-01-04', 26.3, 55, 0.0],
                    ['2024-01-05', 23.7, 68, 5.1]
                ]
            elif data_type == "stock":
                data = [
                    ['Date', 'Symbol', 'Open', 'High', 'Low', 'Close', 'Volume'],
                    ['2024-01-01', 'AAPL', 150.0, 155.2, 149.5, 154.8, 1000000],
                    ['2024-01-02', 'AAPL', 154.8, 158.1, 153.2, 157.5, 1200000],
                    ['2024-01-03', 'AAPL', 157.5, 159.8, 155.0, 156.2, 950000],
                    ['2024-01-04', 'AAPL', 156.2, 161.5, 155.8, 160.9, 1100000],
                    ['2024-01-05', 'AAPL', 160.9, 162.3, 158.7, 159.4, 980000]
                ]
            else:  # custom
                data = [
                    ['ID', 'Name', 'Value', 'Category'],
                    [1, 'Item A', 45.2, 'Type 1'],
                    [2, 'Item B', 67.8, 'Type 2'],
                    [3, 'Item C', 23.1, 'Type 1'],
                    [4, 'Item D', 89.5, 'Type 3'],
                    [5, 'Item E', 34.7, 'Type 2']
                ]
            
            # Write CSV file
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerows(data)
            
            return json5.dumps({
                'filename': filename,
                'data_type': data_type,
                'rows_created': len(data),
                'success': True,
                'preview': data[:3]  # Show first 3 rows as preview
            }, ensure_ascii=False)
            
        except Exception as e:
            return json5.dumps({
                'filename': filename if 'filename' in locals() else 'unknown',
                'error': str(e),
                'success': False
            }, ensure_ascii=False)

def init_working_agent():
    """Initialize working Qwen-Agent with direct UIUC API integration"""
    
    # Configure LLM to use UIUC API directly
    llm_cfg = {
        'model': 'Qwen/Qwen2.5-VL-72B-Instruct',
        'model_server': 'http://localhost:8000/v1',  # Our OpenAI-compatible server
        'api_key': 'EMPTY',
        'generate_cfg': {
            'temperature': 0.1,
            'max_tokens': 2000,
        }
    }
    
    # Define available tools
    tools = [
        'code_executor',    # Custom code execution tool
        'csv_creator',      # Custom CSV creation tool
    ]
    
    # Create the assistant
    bot = Assistant(
        llm=llm_cfg,
        function_list=tools,
        name='Working UIUC Qwen Agent',
        description="""I'm a working AI assistant powered by Qwen2.5-VL-72B-Instruct running on UIUC infrastructure.

🔧 **My Capabilities:**
• **Code Executor** - Execute Python code with pandas, matplotlib, numpy support
• **CSV Creator** - Create CSV files with various types of sample data
• **Data Analysis** - Analyze data and create visualizations
• **Problem Solving** - Break down complex tasks into executable steps

I can actually execute code and create files, not just provide examples!"""
    )
    
    return bot

def test_working_agent(query: str = 'Create a CSV with sales data and then plot it'):
    """Test the working agent"""
    print("🚀 Testing Working Qwen-Agent...")
    print(f"📝 Query: {query}")
    print("=" * 80)
    
    # Initialize the agent
    bot = init_working_agent()
    
    # Run the query
    messages = [{'role': 'user', 'content': query}]
    response_plain_text = ''
    
    for response in bot.run(messages=messages):
        response_plain_text = typewriter_print(response, response_plain_text)
    
    print("\n" + "=" * 80)
    print("✅ Test completed!")

def chat_interface():
    """Interactive chat interface"""
    print("🤖 Working Qwen-Agent Chat Interface")
    print("=" * 60)
    print("🔧 **Capabilities:**")
    print("• Code Executor - Actually execute Python code")
    print("• CSV Creator - Create real CSV files with data")
    print("• Data Analysis - Analyze and visualize data")
    print("• File Operations - Create, read, and manipulate files")
    print("=" * 60)
    print("Commands: /quit (exit), /help (show help)")
    print("=" * 60)
    
    # Initialize the agent
    bot = init_working_agent()
    
    # Chat loop
    messages = []
    while True:
        try:
            query = input('\n💬 You: ').strip()
            
            if not query:
                continue
                
            if query.lower() in ['/quit', '/exit', 'quit', 'exit']:
                print("👋 Goodbye!")
                break
                
            if query.lower() in ['/help', 'help']:
                print("\n🔧 Available Tools:")
                print("• **code_executor** - Execute Python code with full library support")
                print("• **csv_creator** - Create CSV files with sample data")
                print("\n💡 Example queries:")
                print("• 'Create a CSV with sales data and plot a bar chart'")
                print("• 'Generate weather data and create a line plot'")
                print("• 'Calculate the factorial of 10 using Python'")
                print("• 'Create a scatter plot with random data'")
                print("• 'Analyze stock data and show trends'")
                continue
            
            # Add user message
            messages.append({'role': 'user', 'content': query})
            
            print(f"\n🤖 Processing: {query}")
            print("-" * 60)
            
            # Get response
            response = []
            response_plain_text = ''
            
            for response in bot.run(messages=messages):
                response_plain_text = typewriter_print(response, response_plain_text)
            
            # Add response to conversation history
            messages.extend(response)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type /quit to exit.")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            test_working_agent()
        elif sys.argv[1] == 'chat':
            chat_interface()
        else:
            print("Usage: python working_qwen_agent.py [test|chat]")
    else:
        # Default to chat interface
        chat_interface()
