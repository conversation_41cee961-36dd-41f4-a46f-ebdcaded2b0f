#!/usr/bin/env python3
"""
Interactive chat interface for UIUC hosted Qwen2.5-VL-72B-Instruct model
Maintains conversation context and provides a terminal-based chat experience
"""

import requests
import json
import time
from typing import List, Dict

class QwenChat:
    def __init__(self):
        self.url = "https://uiuc.chat/api/chat-api/chat"
        self.headers = {'Content-Type': 'application/json'}
        self.api_key = "uc_0c8d4f0264ec48d0b6a7051b84c496b3"
        self.conversation_history: List[Dict] = []
        self.system_prompt = "You are a helpful AI assistant."
        
    def add_message(self, role: str, content: str):
        """Add a message to conversation history"""
        if content.strip():  # Only add non-empty messages
            self.conversation_history.append({"role": role, "content": content})
    
    def get_messages_for_api(self) -> List[Dict]:
        """Prepare messages for API call"""
        messages = [{"role": "system", "content": self.system_prompt}]
        messages.extend(self.conversation_history)
        return messages
    
    def send_message(self, user_input: str) -> str:
        """Send message and get response"""
        if not user_input.strip():
            return "Please enter a non-empty message."
        
        # Add user message to history
        self.add_message("user", user_input)
        
        # Prepare API request
        data = {
            "model": "Qwen/Qwen2.5-VL-72B-Instruct",
            "messages": self.get_messages_for_api(),
            "api_key": self.api_key,
            "course_name": "modeluse",
            "stream": True,
            "temperature": 0.1,
            "retrieval_only": False
        }
        
        try:
            response = requests.post(self.url, headers=self.headers, json=data)
            
            if response.status_code == 200:
                full_response = ""
                print("Assistant: ", end="", flush=True)
                
                for chunk in response.iter_lines():
                    if chunk:
                        chunk_text = chunk.decode()
                        print(chunk_text, end="", flush=True)
                        full_response += chunk_text
                
                print()  # New line after response
                
                # Add assistant response to history
                if full_response.strip():
                    self.add_message("assistant", full_response)
                
                return full_response
            else:
                error_msg = f"Error {response.status_code}: {response.text}"
                print(f"API Error: {error_msg}")
                return error_msg
                
        except Exception as e:
            error_msg = f"Request failed: {e}"
            print(f"Error: {error_msg}")
            return error_msg
    
    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history = []
        print("Conversation history cleared.")
    
    def show_history(self):
        """Display conversation history"""
        if not self.conversation_history:
            print("No conversation history.")
            return
        
        print("\n--- Conversation History ---")
        for i, msg in enumerate(self.conversation_history, 1):
            role = msg["role"].capitalize()
            content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
            print(f"{i}. {role}: {content}")
        print("--- End History ---\n")
    
    def run_chat(self):
        """Main chat loop"""
        print("🤖 Qwen2.5-VL-72B Chat Interface")
        print("Commands: /clear (clear history), /history (show history), /quit (exit)")
        print("-" * 60)
        
        while True:
            try:
                user_input = input("\nYou: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['/quit', '/exit', '/q']:
                    print("Goodbye!")
                    break
                elif user_input.lower() == '/clear':
                    self.clear_history()
                    continue
                elif user_input.lower() == '/history':
                    self.show_history()
                    continue
                elif user_input.startswith('/'):
                    print("Unknown command. Available: /clear, /history, /quit")
                    continue
                
                # Send message and get response
                self.send_message(user_input)
                
            except KeyboardInterrupt:
                print("\n\nGoodbye!")
                break
            except Exception as e:
                print(f"Unexpected error: {e}")

if __name__ == "__main__":
    chat = QwenChat()
    chat.run_chat()
