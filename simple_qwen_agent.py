#!/usr/bin/env python3
"""
Simple Qwen Agent with Automatic Code Execution
Automatically executes Python code blocks from UIUC model responses
"""

import requests
import json
import re
import subprocess
import tempfile
import os
import sys
from datetime import datetime
from typing import Dict, List, Any

class SimpleQwenAgent:
    def __init__(self):
        self.api_url = "https://uiuc.chat/api/chat-api/chat"
        self.api_key = "uc_0c8d4f0264ec48d0b6a7051b84c496b3"
        self.model = "Qwen/Qwen2.5-VL-72B-Instruct"
        self.course_name = "modeluse"
        self.working_dir = tempfile.mkdtemp(prefix="qwen_agent_")
        print(f"🗂️ Working directory: {self.working_dir}")
        
    def _execute_python_code(self, code: str) -> Dict[str, Any]:
        """Execute Python code and return results"""
        try:
            # Create a temporary Python file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, dir=self.working_dir) as f:
                f.write(code)
                temp_file = f.name
            
            # Execute the code
            result = subprocess.run(
                [sys.executable, temp_file],
                cwd=self.working_dir,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # Clean up
            os.unlink(temp_file)
            
            # Collect output files
            output_files = []
            for file in os.listdir(self.working_dir):
                if file.endswith(('.png', '.jpg', '.jpeg', '.csv', '.txt', '.html')):
                    output_files.append(os.path.join(self.working_dir, file))
            
            return {
                "success": True,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode,
                "output_files": output_files
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Code execution timed out (30s limit)",
                "stdout": "",
                "stderr": "",
                "return_code": -1,
                "output_files": []
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "stdout": "",
                "stderr": "",
                "return_code": -1,
                "output_files": []
            }
    
    def _get_current_time(self) -> str:
        """Get current time information"""
        now = datetime.now()
        return f"Current date and time: {now.strftime('%Y-%m-%d %H:%M:%S')}"
    
    def _parse_and_execute_code(self, response_text: str) -> str:
        """Parse code blocks from response and execute them"""
        # Find Python code blocks
        code_pattern = r'```python\s*(.*?)\s*```'
        code_matches = re.findall(code_pattern, response_text, re.DOTALL)
        
        if not code_matches:
            # Check for time-related queries
            time_keywords = ['time', 'date', 'today', 'now', 'current']
            if any(keyword in response_text.lower() for keyword in time_keywords):
                time_info = self._get_current_time()
                return f"{response_text}\n\n🕒 **Current Time Information:**\n{time_info}"
            return response_text
        
        enhanced_response = response_text
        
        for i, code in enumerate(code_matches):
            print(f"\n🔧 Executing code block {i+1}:")
            print("-" * 40)
            print(code.strip())
            print("-" * 40)
            
            result = self._execute_python_code(code.strip())
            
            execution_summary = f"\n\n🚀 **Code Execution Result {i+1}:**\n"
            
            if result["success"]:
                if result["stdout"]:
                    execution_summary += f"**Output:**\n```\n{result['stdout']}\n```\n"
                
                if result["output_files"]:
                    execution_summary += f"**Files Created:**\n"
                    for file_path in result["output_files"]:
                        file_name = os.path.basename(file_path)
                        execution_summary += f"- {file_name}\n"
                
                if not result["stdout"] and not result["output_files"]:
                    execution_summary += "✅ Code executed successfully (no output)\n"
                else:
                    execution_summary += "✅ Code executed successfully\n"
                    
            else:
                execution_summary += f"❌ **Error:** {result['error']}\n"
                if result["stderr"]:
                    execution_summary += f"**Error Details:**\n```\n{result['stderr']}\n```\n"
            
            enhanced_response += execution_summary
        
        return enhanced_response
    
    def chat(self, message: str, conversation_history: List[Dict] = None) -> str:
        """Send a message and get response with automatic code execution"""
        
        if conversation_history is None:
            conversation_history = []
        
        # Add current message to history
        messages = conversation_history + [{"role": "user", "content": message}]
        
        # Prepare request
        payload = {
            "model": self.model,
            "messages": messages,
            "api_key": self.api_key,
            "course_name": self.course_name,
            "stream": True,
            "temperature": 0.1,
            "retrieval_only": False
        }
        
        headers = {'Content-Type': 'application/json'}
        
        try:
            print(f"🤖 Sending request to UIUC API...")
            response = requests.post(self.api_url, headers=headers, json=payload)
            
            if response.status_code == 200:
                # Handle streaming response
                full_response = ""
                for chunk in response.iter_lines():
                    if chunk:
                        full_response += chunk.decode()

                print(f"📥 Received response from model")

                # Parse and execute any code blocks
                enhanced_response = self._parse_and_execute_code(full_response)

                return enhanced_response
                
            else:
                return f"❌ API Error {response.status_code}: {response.text}"
                
        except Exception as e:
            return f"❌ Request failed: {e}"
    
    def interactive_chat(self):
        """Start an interactive chat session"""
        print("🤖 Simple Qwen Agent with Automatic Code Execution")
        print("=" * 60)
        print("Type 'quit' to exit, 'clear' to clear history")
        print(f"Working directory: {self.working_dir}")
        print("=" * 60)
        
        conversation_history = []
        
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'clear':
                    conversation_history = []
                    print("🗑️ Conversation history cleared")
                    continue
                elif not user_input:
                    continue
                
                # Get response
                response = self.chat(user_input, conversation_history)
                
                # Update conversation history
                conversation_history.append({"role": "user", "content": user_input})
                conversation_history.append({"role": "assistant", "content": response})
                
                # Keep history manageable
                if len(conversation_history) > 10:
                    conversation_history = conversation_history[-10:]
                
                print(f"\n🤖 Assistant: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")

def main():
    """Main function for testing"""
    agent = SimpleQwenAgent()
    
    # Test with the user's original query
    test_query = "can you create a csv with sample data and then create plots on that?"
    
    print("🧪 Testing with user's original query:")
    print(f"Query: {test_query}")
    print("=" * 60)
    
    response = agent.chat(test_query)
    print(f"\n📋 Final Response:\n{response}")
    
    # Start interactive mode
    print("\n" + "=" * 60)
    agent.interactive_chat()

if __name__ == '__main__':
    main()
