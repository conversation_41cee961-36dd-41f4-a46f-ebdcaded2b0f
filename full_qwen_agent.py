#!/usr/bin/env python3
"""
Full Qwen-Agent Implementation with All Capabilities
Uses the official Qwen-Agent framework with our OpenAI-compatible server
"""

import os
from qwen_agent.agents import Assistant
from qwen_agent.gui import WebUI
from qwen_agent.utils.output_beautify import typewriter_print

def init_agent_service():
    """Initialize the full Qwen-Agent with all capabilities"""
    
    # Configure LLM to use our OpenAI-compatible server
    llm_cfg = {
        'model': 'Qwen/Qwen2.5-VL-72B-Instruct',
        'model_server': 'http://localhost:8000/v1',  # Our OpenAI-compatible server
        'api_key': 'EMPTY',  # Not needed for our server
        'generate_cfg': {
            'temperature': 0.1,
            'max_tokens': 2000,
            # When using vLLM/SGLang OAI API, pass the parameter of whether to enable thinking mode in this way
            # 'extra_body': {
            #     'chat_template_kwargs': {'enable_thinking': False}
            # },
            # Add: When the content is `<think>this is the thought</think>this is the answer`
            # Do not add: When the response has been separated by reasoning_content and content
            # This parameter will affect the parsing strategy of tool call
            # 'thought_in_content': True,
        }
    }
    
    # Define comprehensive tools list with all Qwen-Agent capabilities
    tools = [
        # Built-in tools that are actually available
        'code_interpreter',  # Code execution and analysis
    ]
    
    # Create the assistant with comprehensive capabilities
    bot = Assistant(
        llm=llm_cfg,
        function_list=tools,
        name='Enhanced UIUC Qwen Agent',
        description="""I'm an enhanced AI assistant powered by Qwen2.5-VL-72B-Instruct running on UIUC infrastructure.

I have comprehensive capabilities including:
🔧 Code Interpreter - Execute Python code, data analysis, plotting, mathematical calculations

I can help with programming, data analysis, mathematical computations, plotting, and much more!"""
    )
    
    return bot

def test_agent(query: str = 'What time is it? Also, can you show me your capabilities?'):
    """Test the agent with a sample query"""
    print("🚀 Testing Full Qwen-Agent...")
    print(f"📝 Query: {query}")
    print("=" * 80)
    
    # Initialize the agent
    bot = init_agent_service()
    
    # Run the query
    messages = [{'role': 'user', 'content': query}]
    response_plain_text = ''
    
    for response in bot.run(messages=messages):
        response_plain_text = typewriter_print(response, response_plain_text)
    
    print("\n" + "=" * 80)
    print("✅ Test completed!")

def chat_interface():
    """Interactive chat interface"""
    print("🤖 Full Qwen-Agent Chat Interface")
    print("=" * 60)
    print("Available capabilities:")
    print("🔧 Code Interpreter - Execute Python code, data analysis, plotting")
    print("=" * 60)
    print("Commands: /quit (exit), /help (show help)")
    print("=" * 60)
    
    # Initialize the agent
    bot = init_agent_service()
    
    # Chat loop
    messages = []
    while True:
        try:
            query = input('\n💬 You: ').strip()
            
            if not query:
                continue
                
            if query.lower() in ['/quit', '/exit', 'quit', 'exit']:
                print("👋 Goodbye!")
                break
                
            if query.lower() in ['/help', 'help']:
                print("\n🔧 Available Tools:")
                print("• code_interpreter - Execute Python code, data analysis, plotting")
                print("\n💡 Example queries:")
                print("• 'Plot a graph of y=x^2 from -10 to 10'")
                print("• 'Calculate the factorial of 10'")
                print("• 'Analyze data: [1,2,3,4,5]'")
                print("• 'Create a bar chart with sample data'")
                print("• 'What is the current time?' (using Python)")
                continue
            
            # Add user message
            messages.append({'role': 'user', 'content': query})
            
            print(f"\n🤖 Processing: {query}")
            print("-" * 60)
            
            # Get response
            response = []
            response_plain_text = ''
            
            for response in bot.run(messages=messages):
                response_plain_text = typewriter_print(response, response_plain_text)
            
            # Add response to conversation history
            messages.extend(response)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type /quit to exit.")

def web_interface():
    """Launch web GUI interface"""
    print("🌐 Launching Full Qwen-Agent Web Interface...")
    
    # Initialize the agent
    bot = init_agent_service()
    
    # Configure chatbot with suggestions
    chatbot_config = {
        'prompt.suggestions': [
            'Plot a sine wave using Python',
            'Calculate the factorial of 10',
            'Create a simple calculator in Python',
            'Generate a bar chart showing sample data',
            'Analyze this data: [1, 4, 9, 16, 25]',
            'What is the current time using Python?',
            'Plot y=x^2 from -10 to 10',
            'Create a scatter plot with random data'
        ]
    }
    
    # Launch web UI
    WebUI(
        bot,
        chatbot_config=chatbot_config,
    ).run(server_name="0.0.0.0", server_port=7860)

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            test_agent()
        elif sys.argv[1] == 'web':
            web_interface()
        elif sys.argv[1] == 'chat':
            chat_interface()
        else:
            print("Usage: python full_qwen_agent.py [test|chat|web]")
    else:
        # Default to chat interface
        chat_interface()
